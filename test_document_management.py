#!/usr/bin/env python3
"""
Test script for the Document Management feature in the Django Claims Application.
This script tests all document-related functionality including upload, download,
view, edit, and delete operations with proper permission checks.
"""

import os
import sys
import django
import tempfile
from io import BytesIO

# Setup Django environment
sys.path.append('/Users/<USER>/demo-claims')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'claims_system.settings.development')
django.setup()

from django.contrib.auth import get_user_model
from django.test import TestCase, Client
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from claims.models import Claim
from documents.models import Document

User = get_user_model()

class DocumentManagementTest:
    def __init__(self):
        self.client = Client()
        self.base_url = 'http://127.0.0.1:8000'
        self.setup_test_data()

    def setup_test_data(self):
        """Create test users and claims"""
        print("Setting up test data...")

        # Create test users
        self.customer = User.objects.filter(user_type='customer').first()
        self.adjuster = User.objects.filter(user_type='adjuster').first()
        self.admin = User.objects.filter(user_type='admin').first()

        if not self.customer:
            self.customer = User.objects.create_user(
                username='testcustomer',
                email='<EMAIL>',
                password='testpass123',
                user_type='customer',
                first_name='Test',
                last_name='Customer'
            )

        if not self.adjuster:
            self.adjuster = User.objects.create_user(
                username='testadjuster',
                email='<EMAIL>',
                password='testpass123',
                user_type='adjuster',
                first_name='Test',
                last_name='Adjuster'
            )

        # Get or create a test claim
        self.claim = Claim.objects.filter(claimant=self.customer).first()
        if not self.claim:
            self.claim = Claim.objects.create(
                claimant=self.customer,
                claim_type='auto',
                incident_date='2024-01-15',
                incident_location='Test Location',
                incident_description='Test incident for document management testing',
                estimated_damage_amount=5000.00
            )

        print(f"Test claim: {self.claim.claim_number}")

    def create_test_files(self):
        """Create test files for upload"""
        # Create test files
        self.test_files = {
            'image': SimpleUploadedFile(
                'test_image.jpg',
                b'fake image content',
                content_type='image/jpeg'
            ),
            'pdf': SimpleUploadedFile(
                'test_document.pdf',
                b'%PDF-1.4 fake pdf content',
                content_type='application/pdf'
            ),
            'text': SimpleUploadedFile(
                'test_document.txt',
                b'This is a test document content',
                content_type='text/plain'
            )
        }

    def test_login(self, user):
        """Login as a specific user"""
        login_success = self.client.login(username=user.username, password='testpass123')
        print(f"Login as {user.username}: {'Success' if login_success else 'Failed'}")
        return login_success

    def test_document_upload_page(self):
        """Test document upload page access"""
        print("\n=== Testing Document Upload Page ===")

        # Test as customer
        self.test_login(self.customer)
        url = reverse('documents:upload', kwargs={'claim_id': self.claim.id})
        response = self.client.get(url)
        print(f"Customer access to upload page: {response.status_code}")

        # Test as adjuster
        self.test_login(self.adjuster)
        response = self.client.get(url)
        print(f"Adjuster access to upload page: {response.status_code}")

    def test_document_upload(self):
        """Test document upload functionality"""
        print("\n=== Testing Document Upload ===")
        self.create_test_files()

        # Test as customer
        self.test_login(self.customer)

        for file_type, file_obj in self.test_files.items():
            url = reverse('documents:upload', kwargs={'claim_id': self.claim.id})
            data = {
                'title': f'Test {file_type} document',
                'description': f'This is a test {file_type} document',
                'document_type': 'photo' if file_type == 'image' else 'other',
                'file': file_obj
            }

            response = self.client.post(url, data)
            print(f"Upload {file_type}: {response.status_code}")

            if response.status_code == 302:  # Redirect on success
                print(f"  ✓ {file_type} upload successful")
            else:
                print(f"  ✗ {file_type} upload failed")

    def test_ajax_upload(self):
        """Test AJAX document upload"""
        print("\n=== Testing AJAX Upload ===")
        self.create_test_files()

        self.test_login(self.customer)

        url = reverse('documents:ajax_upload', kwargs={'claim_id': self.claim.id})

        # Test image upload via AJAX
        data = {
            'file': self.test_files['image'],
            'document_type': 'photo',
            'is_confidential': 'false'
        }

        response = self.client.post(url, data)
        print(f"AJAX upload response: {response.status_code}")

        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"  ✓ AJAX upload successful: {response_data.get('title', 'Unknown')}")
            except:
                print("  ✗ AJAX upload failed - invalid JSON response")
        else:
            print(f"  ✗ AJAX upload failed with status {response.status_code}")

    def test_document_list(self):
        """Test document list page"""
        print("\n=== Testing Document List ===")

        # Test as customer
        self.test_login(self.customer)
        url = reverse('documents:list', kwargs={'claim_id': self.claim.id})
        response = self.client.get(url)
        print(f"Customer document list access: {response.status_code}")

        # Test as adjuster
        self.test_login(self.adjuster)
        response = self.client.get(url)
        print(f"Adjuster document list access: {response.status_code}")

    def test_document_operations(self):
        """Test document view, edit, download, delete operations"""
        print("\n=== Testing Document Operations ===")

        # Get a test document
        document = Document.objects.filter(claim=self.claim).first()
        if not document:
            print("No documents found for testing operations")
            return

        print(f"Testing with document: {document.title}")

        # Test document view
        self.test_login(self.customer)
        url = reverse('documents:view', kwargs={'document_id': document.id})
        response = self.client.get(url)
        print(f"Document view: {response.status_code}")

        # Test document download
        url = reverse('documents:download', kwargs={'document_id': document.id})
        response = self.client.get(url)
        print(f"Document download: {response.status_code}")

        # Test document edit (as adjuster)
        self.test_login(self.adjuster)
        url = reverse('documents:edit', kwargs={'document_id': document.id})
        response = self.client.get(url)
        print(f"Document edit page: {response.status_code}")

        # Test document edit POST
        data = {
            'title': 'Updated Document Title',
            'description': 'Updated description',
            'document_type': 'estimate',
            'is_approved': True
        }
        response = self.client.post(url, data)
        print(f"Document edit submit: {response.status_code}")

    def test_permission_checks(self):
        """Test permission checks for document operations"""
        print("\n=== Testing Permission Checks ===")

        # Create a document as customer
        self.test_login(self.customer)
        document = Document.objects.filter(claim=self.claim, uploaded_by=self.customer).first()

        if document:
            # Test customer trying to access confidential document
            document.is_confidential = True
            document.save()

            url = reverse('documents:view', kwargs={'document_id': document.id})
            response = self.client.get(url)
            print(f"Customer accessing confidential doc: {response.status_code}")

            # Test adjuster accessing same document
            self.test_login(self.adjuster)
            response = self.client.get(url)
            print(f"Adjuster accessing confidential doc: {response.status_code}")

    def test_claims_detail_integration(self):
        """Test document section in claims detail page"""
        print("\n=== Testing Claims Detail Integration ===")

        self.test_login(self.customer)
        url = reverse('claims:detail', kwargs={'claim_id': self.claim.id})
        response = self.client.get(url)
        print(f"Claims detail with documents: {response.status_code}")

        if response.status_code == 200:
            content = response.content.decode()
            if 'Documents' in content:
                print("  ✓ Documents section found in claims detail")
            else:
                print("  ✗ Documents section not found in claims detail")

    def run_all_tests(self):
        """Run all document management tests"""
        print("Starting Document Management Tests...")
        print("=" * 50)

        try:
            self.test_document_upload_page()
            self.test_document_upload()
            self.test_ajax_upload()
            self.test_document_list()
            self.test_document_operations()
            self.test_permission_checks()
            self.test_claims_detail_integration()

            print("\n" + "=" * 50)
            print("Document Management Tests Completed!")

            # Print summary
            total_docs = Document.objects.filter(claim=self.claim).count()
            print(f"Total documents created during testing: {total_docs}")

        except Exception as e:
            print(f"Test failed with error: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    tester = DocumentManagementTest()
    tester.run_all_tests()
