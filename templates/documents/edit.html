{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Edit {{ document.title }} - Claim {{ claim.claim_number }} - IMT Insurance Claims{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'claims:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'claims:detail' claim.id %}">{{ claim.claim_number }}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'documents:view' document.id %}">{{ document.title|truncatechars:20 }}</a></li>
                <li class="breadcrumb-item active">Edit</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-edit me-2"></i>Edit Document Details
                </h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    {{ form|crispy }}
                    
                    <div class="d-flex gap-2 mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Changes
                        </button>
                        <a href="{% url 'documents:view' document.id %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Current Document Info -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Current Document
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    {% if document.is_image %}
                        <i class="fas fa-file-image fa-3x text-info"></i>
                    {% elif document.is_pdf %}
                        <i class="fas fa-file-pdf fa-3x text-danger"></i>
                    {% elif document.is_document %}
                        <i class="fas fa-file-word fa-3x text-primary"></i>
                    {% elif document.is_spreadsheet %}
                        <i class="fas fa-file-excel fa-3x text-success"></i>
                    {% else %}
                        <i class="fas fa-file fa-3x text-secondary"></i>
                    {% endif %}
                </div>
                
                <p class="mb-1"><strong>File:</strong> {{ document.original_filename }}</p>
                <p class="mb-1"><strong>Size:</strong> {{ document.file_size_formatted }}</p>
                <p class="mb-1"><strong>Uploaded:</strong> {{ document.created_at|date:"M d, Y" }}</p>
                <p class="mb-1"><strong>By:</strong> {{ document.uploaded_by.full_name }}</p>
                
                <div class="mt-3">
                    <a href="{% url 'documents:download' document.id %}" class="btn btn-sm btn-outline-success">
                        <i class="fas fa-download me-1"></i>Download
                    </a>
                    <a href="{% url 'documents:view' document.id %}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye me-1"></i>View
                    </a>
                </div>
            </div>
        </div>

        <!-- Edit Guidelines -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Edit Guidelines
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Update the title to be descriptive
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Add a detailed description if needed
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Select the appropriate document type
                    </li>
                    {% if user.user_type in 'adjuster,admin' %}
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Mark as confidential if sensitive
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Approve document if verified
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
