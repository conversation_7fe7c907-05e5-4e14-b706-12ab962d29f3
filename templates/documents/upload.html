{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Upload Document - Claim {{ claim.claim_number }} - IMT Insurance Claims{% endblock %}

{% block extra_css %}
<link href="{% static 'css/dropzone.min.css' %}" rel="stylesheet" type="text/css" />
<style>
.dropzone-custom {
    border: 3px dashed var(--imt-red) !important;
    border-radius: 15px !important;
    background: linear-gradient(135deg, var(--imt-white) 0%, var(--imt-light-gray) 100%) !important;
    min-height: 200px !important;
    padding: 20px !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
}

.dropzone {
    border: 3px dashed var(--imt-red) !important;
    border-radius: 15px !important;
    background: linear-gradient(135deg, var(--imt-white) 0%, var(--imt-light-gray) 100%) !important;
    min-height: 200px !important;
    padding: 20px !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
}

.dropzone:hover {
    border-color: var(--imt-dark-red) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 10px 25px rgba(229, 62, 62, 0.2) !important;
}

.dropzone.dz-drag-hover {
    border-color: var(--imt-dark-red) !important;
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%) !important;
    transform: scale(1.02) !important;
}

.dropzone .dz-message {
    font-size: 1.2rem !important;
    color: var(--imt-text) !important;
    font-weight: 600 !important;
}

.dropzone .dz-message .note {
    font-size: 0.9rem !important;
    color: #666 !important;
    font-weight: normal !important;
    margin-top: 10px !important;
}

.dz-preview {
    margin: 10px !important;
}

.dz-preview .dz-image {
    border-radius: 10px !important;
}

.dz-preview .dz-details {
    background: rgba(255, 255, 255, 0.9) !important;
    border-radius: 10px !important;
}

.dz-preview .dz-progress {
    background: var(--imt-light-gray) !important;
    border-radius: 10px !important;
}

.dz-preview .dz-progress .dz-upload {
    background: linear-gradient(135deg, var(--imt-red) 0%, var(--imt-dark-red) 100%) !important;
    border-radius: 10px !important;
}

.document-type-selector {
    background: var(--imt-white);
    border: 2px solid var(--imt-border);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'claims:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'claims:detail' claim.id %}">{{ claim.claim_number }}</a></li>
                <li class="breadcrumb-item active">Upload Document</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-upload me-2"></i>Upload Documents
                </h4>
                <small class="text-light">Claim: {{ claim.claim_number }}</small>
            </div>
            <div class="card-body">
                <!-- Document Type Selector -->
                <div class="document-type-selector">
                    <label for="document-type" class="form-label">
                        <i class="fas fa-tag me-2"></i>Document Type
                    </label>
                    <select id="document-type" class="form-select">
                        {% for value, label in form.document_type.field.choices %}
                            <option value="{{ value }}">{{ label }}</option>
                        {% endfor %}
                    </select>

                    {% if user.user_type in 'adjuster,admin' %}
                    <div class="form-check mt-3">
                        <input class="form-check-input" type="checkbox" id="is-confidential">
                        <label class="form-check-label" for="is-confidential">
                            <i class="fas fa-lock me-1"></i>Mark as Confidential
                        </label>
                    </div>
                    {% endif %}
                </div>

                <!-- Dropzone Area -->
                <div id="document-dropzone" class="dropzone-custom">
                    <div class="dz-message">
                        <i class="fas fa-cloud-upload-alt fa-3x mb-3" style="color: var(--imt-red);"></i><br>
                        <strong>Drop files here or click to upload</strong>
                        <div class="note">
                            Supported formats: PDF, DOC, DOCX, JPG, PNG, GIF, XLS, XLSX, ZIP<br>
                            Maximum file size: 10MB per file
                        </div>
                    </div>
                </div>

                <!-- Traditional Upload Form (Fallback) -->
                <div class="mt-4">
                    <h6>Or use traditional upload:</h6>
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        {{ form|crispy }}
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>Upload Document
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Upload Guidelines -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Upload Guidelines
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Maximum file size: 10MB
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Multiple files supported
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Drag & drop enabled
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-file-pdf text-danger me-2"></i>
                        PDF documents
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-file-word text-primary me-2"></i>
                        Word documents
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-file-image text-info me-2"></i>
                        Images (JPG, PNG, GIF)
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-file-excel text-success me-2"></i>
                        Spreadsheets
                    </li>
                </ul>
            </div>
        </div>

        <!-- Recent Documents -->
        {% if claim.documents.all %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-history me-2"></i>Recent Documents
                </h6>
            </div>
            <div class="card-body">
                {% for document in claim.documents.all|slice:":5" %}
                    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                        <div>
                            <strong>{{ document.title|truncatechars:25 }}</strong><br>
                            <small class="text-muted">{{ document.get_document_type_display }}</small>
                        </div>
                        <small class="text-muted">{{ document.created_at|date:"M d" }}</small>
                    </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Disable Dropzone auto-discovery BEFORE loading the library
window.Dropzone = { autoDiscover: false };
</script>
<script src="{% static 'js/dropzone.min.js' %}"></script>
<script>
// Fallback function if Dropzone fails to load
function dropzoneFailed() {
    console.error('Dropzone.js failed to load');
    document.getElementById('document-dropzone').innerHTML =
        '<div class="alert alert-warning">' +
        '<i class="fas fa-exclamation-triangle me-2"></i>' +
        'Drag & drop upload is not available. Please use the traditional upload form below.' +
        '</div>';
}

// Function to show messages safely
function showMessage(type, message) {
    // Remove any existing alerts
    const existingAlerts = document.querySelectorAll('.alert.upload-message');
    existingAlerts.forEach(alert => alert.remove());

    // Create new alert
    const alertDiv = document.createElement('div');
    let alertClass = 'danger'; // default
    if (type === 'success') alertClass = 'success';
    else if (type === 'info') alertClass = 'info';
    else if (type === 'warning') alertClass = 'warning';

    alertDiv.className = `alert alert-${alertClass} alert-dismissible fade show upload-message`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Find a safe place to insert the message
    const cardBody = document.querySelector('.card-body');
    if (cardBody) {
        cardBody.insertBefore(alertDiv, cardBody.firstChild);
    } else {
        // Fallback: insert at the top of the page
        const mainContent = document.querySelector('main') || document.body;
        mainContent.insertBefore(alertDiv, mainContent.firstChild);
    }

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv && alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function initializeDropzone() {
    // Check if Dropzone is loaded
    if (typeof Dropzone === 'undefined') {
        console.error('Dropzone.js failed to load');
        dropzoneFailed();
        return;
    }

    // Make sure auto-discovery is disabled
    Dropzone.autoDiscover = false;

    // Check if element already has dropzone
    const element = document.getElementById('document-dropzone');
    if (element.dropzone) {
        console.log('Dropzone already initialized, destroying first');
        element.dropzone.destroy();
    }

    // Get CSRF token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    if (!csrfToken) {
        console.error('CSRF token not found');
        dropzoneFailed();
        return;
    }

    // Initialize Dropzone
    const dropzone = new Dropzone("#document-dropzone", {
        url: "{% url 'documents:ajax_upload' claim.id %}",
        paramName: "file",
        maxFilesize: 10, // MB
        acceptedFiles: ".pdf,.doc,.docx,.txt,.rtf,.jpg,.jpeg,.png,.gif,.bmp,.tiff,.xls,.xlsx,.csv,.zip,.rar",
        addRemoveLinks: true,
        dictDefaultMessage: "",
        init: function() {
            this.on("sending", function(file, xhr, formData) {
                // Add CSRF token
                formData.append("csrfmiddlewaretoken", csrfToken);
                // Add document type and confidential flag
                formData.append("document_type", document.getElementById('document-type').value);
                {% if user.user_type in 'adjuster,admin' %}
                formData.append("is_confidential", document.getElementById('is-confidential').checked);
                {% endif %}

                // Show uploading message
                showMessage('info', `Uploading "${file.name}"...`);
            });

            this.on("success", function(file, response) {
                console.log("Upload successful:", response);
                showMessage('success', `Document "${response.title}" uploaded successfully!`);

                // Optional: Add a link to view the uploaded document
                setTimeout(() => {
                    const viewLink = document.createElement('div');
                    viewLink.className = 'mt-2';
                    viewLink.innerHTML = `
                        <a href="/documents/${response.document_id}/" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye me-1"></i>View Document
                        </a>
                    `;
                    const lastAlert = document.querySelector('.alert.upload-message:last-child');
                    if (lastAlert) {
                        lastAlert.appendChild(viewLink);
                    }
                }, 1000);
            });

            this.on("error", function(file, response) {
                console.error("Upload failed:", response);
                let errorMsg = 'Unknown error';

                if (typeof response === 'string') {
                    errorMsg = response;
                } else if (response && response.error) {
                    errorMsg = response.error;
                } else if (response && response.responseText) {
                    try {
                        const parsed = JSON.parse(response.responseText);
                        errorMsg = parsed.error || response.responseText;
                    } catch (e) {
                        errorMsg = response.responseText;
                    }
                }

                showMessage('error', `Upload failed: ${errorMsg}`);
            });
        }
    });
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Wait for Dropzone to be fully loaded
    if (typeof Dropzone !== 'undefined') {
        initializeDropzone();
    } else {
        // Retry after a short delay
        setTimeout(function() {
            if (typeof Dropzone !== 'undefined') {
                initializeDropzone();
            } else {
                console.error('Dropzone failed to load after retry');
                dropzoneFailed();
            }
        }, 500);
    }
});
</script>
{% endblock %}
