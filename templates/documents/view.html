{% extends 'base.html' %}
{% load static %}

{% block title %}{{ document.title }} - Claim {{ claim.claim_number }} - IMT Insurance Claims{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'claims:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'claims:detail' claim.id %}">{{ claim.claim_number }}</a></li>
                <li class="breadcrumb-item"><a href="{% url 'documents:list' claim.id %}">Documents</a></li>
                <li class="breadcrumb-item active">{{ document.title|truncatechars:30 }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Document Details -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    {% if document.is_image %}
                        <i class="fas fa-file-image text-info me-2"></i>
                    {% elif document.is_pdf %}
                        <i class="fas fa-file-pdf text-danger me-2"></i>
                    {% elif document.is_document %}
                        <i class="fas fa-file-word text-primary me-2"></i>
                    {% elif document.is_spreadsheet %}
                        <i class="fas fa-file-excel text-success me-2"></i>
                    {% else %}
                        <i class="fas fa-file text-secondary me-2"></i>
                    {% endif %}
                    {{ document.title }}
                </h4>
                <div>
                    {% if document.is_confidential %}
                        <span class="badge bg-warning text-dark me-2">
                            <i class="fas fa-lock"></i> Confidential
                        </span>
                    {% endif %}
                    {% if document.is_approved %}
                        <span class="badge bg-success">
                            <i class="fas fa-check"></i> Approved
                        </span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <!-- Document Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6><i class="fas fa-info-circle me-1"></i>Document Information</h6>
                        <p class="mb-1"><strong>Type:</strong> {{ document.get_document_type_display }}</p>
                        <p class="mb-1"><strong>File Size:</strong> {{ document.file_size_formatted }}</p>
                        <p class="mb-1"><strong>File Format:</strong> {{ document.file_extension|upper }}</p>
                        <p class="mb-1"><strong>Original Filename:</strong> {{ document.original_filename }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-clock me-1"></i>Upload Information</h6>
                        <p class="mb-1"><strong>Uploaded By:</strong> {{ document.uploaded_by.full_name }}</p>
                        <p class="mb-1"><strong>Upload Date:</strong> {{ document.created_at|date:"M d, Y g:i A" }}</p>
                        {% if document.updated_at != document.created_at %}
                        <p class="mb-1"><strong>Last Modified:</strong> {{ document.updated_at|date:"M d, Y g:i A" }}</p>
                        {% endif %}
                    </div>
                </div>

                {% if document.description %}
                <div class="mb-4">
                    <h6><i class="fas fa-align-left me-1"></i>Description</h6>
                    <p>{{ document.description|linebreaks }}</p>
                </div>
                {% endif %}

                <!-- Document Preview -->
                <div class="mb-4">
                    <h6><i class="fas fa-eye me-1"></i>Preview</h6>
                    {% if document.is_image %}
                        <div class="text-center">
                            <img src="{{ document.file.url }}" 
                                 alt="{{ document.title }}" 
                                 class="img-fluid rounded shadow"
                                 style="max-height: 500px;">
                        </div>
                    {% elif document.is_pdf %}
                        <div class="text-center">
                            <embed src="{{ document.file.url }}" 
                                   type="application/pdf" 
                                   width="100%" 
                                   height="500px"
                                   class="rounded shadow">
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-file fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">Preview not available</h5>
                            <p class="text-muted">Download the file to view its contents.</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Action Buttons -->
                <div class="d-flex gap-2 flex-wrap">
                    <a href="{% url 'documents:download' document.id %}" class="btn btn-success">
                        <i class="fas fa-download me-2"></i>Download
                    </a>
                    {% if user.user_type in 'adjuster,admin' or document.uploaded_by == user %}
                    <a href="{% url 'documents:edit' document.id %}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Edit Details
                    </a>
                    {% endif %}
                    {% if document.can_be_deleted_by:user %}
                    <form method="post" action="{% url 'documents:delete' document.id %}" 
                          style="display: inline;" 
                          onsubmit="return confirm('Are you sure you want to delete this document?');">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>Delete
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Claim Information -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-clipboard-list me-2"></i>Related Claim
                </h6>
            </div>
            <div class="card-body">
                <h6><a href="{% url 'claims:detail' claim.id %}">{{ claim.claim_number }}</a></h6>
                <p class="mb-1"><strong>Status:</strong> <span class="badge status-{{ claim.status }}">{{ claim.get_status_display }}</span></p>
                <p class="mb-1"><strong>Type:</strong> {{ claim.get_claim_type_display }}</p>
                <p class="mb-1"><strong>Claimant:</strong> {{ claim.claimant.full_name }}</p>
                <p class="mb-0"><strong>Created:</strong> {{ claim.created_at|date:"M d, Y" }}</p>
            </div>
        </div>

        <!-- Document Versions -->
        {% if document.versions.all %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-history me-2"></i>Document Versions
                </h6>
            </div>
            <div class="card-body">
                {% for version in document.versions.all %}
                <div class="border-bottom pb-2 mb-2">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <strong>Version {{ version.version_number }}</strong><br>
                            <small class="text-muted">{{ version.uploaded_by.full_name }}</small>
                        </div>
                        <small class="text-muted">{{ version.created_at|date:"M d, Y" }}</small>
                    </div>
                    {% if version.change_notes %}
                    <p class="mt-1 mb-0 small">{{ version.change_notes }}</p>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Related Documents -->
        {% with related_docs=claim.documents.all %}
        {% if related_docs.count > 1 %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-folder me-2"></i>Other Documents
                </h6>
            </div>
            <div class="card-body">
                {% for doc in related_docs %}
                    {% if doc.id != document.id %}
                    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                        <div>
                            <a href="{% url 'documents:view' doc.id %}">{{ doc.title|truncatechars:25 }}</a><br>
                            <small class="text-muted">{{ doc.get_document_type_display }}</small>
                        </div>
                        <small class="text-muted">{{ doc.created_at|date:"M d" }}</small>
                    </div>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
        {% endif %}
        {% endwith %}
    </div>
</div>
{% endblock %}
