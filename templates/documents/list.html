{% extends 'base.html' %}
{% load static %}

{% block title %}Documents - Claim {{ claim.claim_number }} - IMT Insurance Claims{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'claims:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'claims:detail' claim.id %}">{{ claim.claim_number }}</a></li>
                <li class="breadcrumb-item active">Documents</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-folder-open me-2"></i>Documents for Claim {{ claim.claim_number }}
                </h4>
                <div>
                    <a href="{% url 'documents:upload' claim.id %}" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>Upload Document
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if documents %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-file me-1"></i>Document</th>
                                    <th><i class="fas fa-tag me-1"></i>Type</th>
                                    <th><i class="fas fa-hdd me-1"></i>Size</th>
                                    <th><i class="fas fa-user me-1"></i>Uploaded By</th>
                                    <th><i class="fas fa-calendar me-1"></i>Date</th>
                                    <th><i class="fas fa-cog me-1"></i>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for document in documents %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if document.is_image %}
                                                <i class="fas fa-file-image text-info me-2"></i>
                                            {% elif document.is_pdf %}
                                                <i class="fas fa-file-pdf text-danger me-2"></i>
                                            {% elif document.is_document %}
                                                <i class="fas fa-file-word text-primary me-2"></i>
                                            {% elif document.is_spreadsheet %}
                                                <i class="fas fa-file-excel text-success me-2"></i>
                                            {% else %}
                                                <i class="fas fa-file text-secondary me-2"></i>
                                            {% endif %}
                                            <div>
                                                <strong>{{ document.title }}</strong>
                                                {% if document.is_confidential %}
                                                    <span class="badge bg-warning text-dark ms-2">
                                                        <i class="fas fa-lock"></i> Confidential
                                                    </span>
                                                {% endif %}
                                                {% if document.is_approved %}
                                                    <span class="badge bg-success ms-2">
                                                        <i class="fas fa-check"></i> Approved
                                                    </span>
                                                {% endif %}
                                                {% if document.description %}
                                                    <br><small class="text-muted">{{ document.description|truncatechars:50 }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ document.get_document_type_display }}</span>
                                    </td>
                                    <td>{{ document.file_size_formatted }}</td>
                                    <td>{{ document.uploaded_by.full_name }}</td>
                                    <td>{{ document.created_at|date:"M d, Y g:i A" }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'documents:view' document.id %}" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'documents:download' document.id %}" 
                                               class="btn btn-sm btn-outline-success" 
                                               title="Download">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            {% if user.user_type in 'adjuster,admin' or document.uploaded_by == user %}
                                            <a href="{% url 'documents:edit' document.id %}" 
                                               class="btn btn-sm btn-outline-warning" 
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                            {% if document.can_be_deleted_by:user %}
                                            <form method="post" action="{% url 'documents:delete' document.id %}" 
                                                  style="display: inline;" 
                                                  onsubmit="return confirm('Are you sure you want to delete this document?');">
                                                {% csrf_token %}
                                                <button type="submit" 
                                                        class="btn btn-sm btn-outline-danger" 
                                                        title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-folder-open fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">No documents uploaded yet</h5>
                        <p class="text-muted">Upload your first document to get started.</p>
                        <a href="{% url 'documents:upload' claim.id %}" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>Upload Document
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Document Statistics -->
{% if documents %}
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ documents.count }}</h3>
                <p class="mb-0">Total Documents</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">{{ documents|length }}</h3>
                <p class="mb-0">Accessible to You</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                {% with approved_count=documents.filter:is_approved=True.count %}
                <h3 class="text-info">{{ approved_count }}</h3>
                <p class="mb-0">Approved</p>
                {% endwith %}
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                {% with total_size=documents.aggregate:total_size=Sum:file_size.total_size|default:0 %}
                <h3 class="text-warning">
                    {% if total_size %}
                        {% if total_size < 1048576 %}
                            {{ total_size|filesizeformat }}
                        {% else %}
                            {{ total_size|filesizeformat }}
                        {% endif %}
                    {% else %}
                        0 B
                    {% endif %}
                </h3>
                <p class="mb-0">Total Size</p>
                {% endwith %}
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
