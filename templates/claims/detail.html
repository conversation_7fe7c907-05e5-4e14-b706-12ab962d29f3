{% extends 'base.html' %}
{% load static %}

{% block title %}Claim {{ claim.claim_number }} - IMT Insurance Claims{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'claims:dashboard' %}">Dashboard</a></li>
                {% if user.user_type == 'customer' %}
                    <li class="breadcrumb-item"><a href="{% url 'claims:my_claims' %}">My Claims</a></li>
                {% else %}
                    <li class="breadcrumb-item"><a href="{% url 'claims:list' %}">Claims</a></li>
                {% endif %}
                <li class="breadcrumb-item active">{{ claim.claim_number }}</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Claim Information -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-clipboard-list me-2"></i>Claim {{ claim.claim_number }}
                </h4>
                <div>
                    <span class="badge status-{{ claim.status }} me-2">{{ claim.get_status_display }}</span>
                    <span class="badge priority-{{ claim.priority }}">{{ claim.get_priority_display }}</span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-user me-1"></i>Claimant Information</h6>
                        <p class="mb-1"><strong>Name:</strong> {{ claim.claimant.full_name }}</p>
                        <p class="mb-1"><strong>Email:</strong> {{ claim.claimant.email }}</p>
                        <p class="mb-1"><strong>Phone:</strong> {{ claim.claimant.phone_number|default:"Not provided" }}</p>
                        {% if claim.claimant.policy_number %}
                            <p class="mb-3"><strong>Policy:</strong> {{ claim.claimant.policy_number }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-info-circle me-1"></i>Claim Details</h6>
                        <p class="mb-1"><strong>Type:</strong> {{ claim.get_claim_type_display }}</p>
                        <p class="mb-1"><strong>Submitted:</strong> {{ claim.created_at|date:"M d, Y g:i A" }}</p>
                        {% if claim.assigned_adjuster %}
                            <p class="mb-1"><strong>Adjuster:</strong> {{ claim.assigned_adjuster.full_name }}</p>
                        {% endif %}
                        <p class="mb-3"><strong>Last Updated:</strong> {{ claim.updated_at|date:"M d, Y g:i A" }}</p>
                    </div>
                </div>

                <hr>

                <h6><i class="fas fa-exclamation-triangle me-1"></i>Incident Information</h6>
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Date:</strong> {{ claim.incident_date|date:"M d, Y g:i A" }}</p>
                        <p class="mb-3"><strong>Location:</strong> {{ claim.incident_location }}</p>
                    </div>
                    <div class="col-md-6">
                        {% if claim.estimated_damage_amount %}
                            <p class="mb-1"><strong>Estimated Damage:</strong> ${{ claim.estimated_damage_amount|floatformat:2 }}</p>
                        {% endif %}
                        {% if claim.approved_amount %}
                            <p class="mb-1"><strong>Approved Amount:</strong> ${{ claim.approved_amount|floatformat:2 }}</p>
                        {% endif %}
                        {% if claim.deductible_amount %}
                            <p class="mb-3"><strong>Deductible:</strong> ${{ claim.deductible_amount|floatformat:2 }}</p>
                        {% endif %}
                    </div>
                </div>

                <h6><i class="fas fa-file-alt me-1"></i>Description</h6>
                <p class="mb-0">{{ claim.incident_description|linebreaks }}</p>
            </div>
            {% if can_edit and user.user_type in 'adjuster,admin' %}
                <div class="card-footer">
                    <a href="{% url 'claims:edit' claim.id %}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit Claim
                    </a>
                </div>
            {% endif %}
        </div>

        <!-- Notes Section -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-comments me-2"></i>Notes & Communication
                </h5>
            </div>
            <div class="card-body">
                {% if notes %}
                    {% for note in notes %}
                        <div class="border-bottom pb-3 mb-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong>{{ note.author.full_name }}</strong>
                                    {% if note.is_internal and user.user_type != 'customer' %}
                                        <span class="badge bg-warning text-dark ms-2">Internal</span>
                                    {% endif %}
                                </div>
                                <small class="text-muted">{{ note.created_at|date:"M d, Y g:i A" }}</small>
                            </div>
                            <p class="mt-2 mb-0">{{ note.note|linebreaks }}</p>
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">No notes yet.</p>
                {% endif %}

                <!-- Add Note Form -->
                <form method="post" action="{% url 'claims:add_note' claim.id %}">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="id_note" class="form-label">Add Note</label>
                        <textarea name="note" class="form-control" rows="3" placeholder="Add your note here..." required></textarea>
                    </div>
                    {% if user.user_type != 'customer' %}
                        <div class="form-check mb-3">
                            <input type="checkbox" name="is_internal" class="form-check-input" id="is_internal">
                            <label class="form-check-label" for="is_internal">
                                Internal note (not visible to customer)
                            </label>
                        </div>
                    {% endif %}
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>Add Note
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h6>
            </div>
            <div class="card-body">
                {% if user.user_type != 'customer' %}
                    <div class="d-grid gap-2">
                        <a href="{% url 'claims:add_task' claim.id %}" class="btn btn-outline-primary">
                            <i class="fas fa-plus me-2"></i>Add Task
                        </a>
                        <a href="{% url 'documents:upload' claim.id %}" class="btn btn-outline-secondary">
                            <i class="fas fa-upload me-2"></i>Upload Document
                        </a>
                        <a href="{% url 'payments:create' claim.id %}" class="btn btn-outline-success">
                            <i class="fas fa-dollar-sign me-2"></i>Create Payment
                        </a>
                    </div>
                {% else %}
                    <div class="d-grid gap-2">
                        <a href="{% url 'documents:upload' claim.id %}" class="btn btn-outline-secondary">
                            <i class="fas fa-upload me-2"></i>Upload Document
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Tasks -->
        {% if tasks %}
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-tasks me-2"></i>Tasks
                    </h6>
                </div>
                <div class="card-body">
                    {% for task in tasks %}
                        <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                            <div>
                                <strong>{{ task.title }}</strong><br>
                                <small class="text-muted">Assigned to: {{ task.assigned_to.full_name }}</small>
                            </div>
                            <div>
                                <span class="badge bg-{{ task.status|yesno:'success,warning,secondary' }}">
                                    {{ task.get_status_display }}
                                </span>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}

        <!-- Documents Section -->
        <div class="card mt-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-folder-open me-2"></i>Documents
                    {% if documents %}
                        <span class="badge bg-light text-dark ms-2">{{ documents.count }}</span>
                    {% endif %}
                </h6>
                <div class="btn-group" role="group">
                    <a href="{% url 'documents:upload' claim.id %}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-upload"></i>
                    </a>
                    {% if documents %}
                    <a href="{% url 'documents:list' claim.id %}" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-list"></i>
                    </a>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                {% if documents %}
                    {% for document in documents|slice:":5" %}
                        <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                            <div class="d-flex align-items-center">
                                {% if document.is_image %}
                                    <i class="fas fa-file-image text-info me-2"></i>
                                {% elif document.is_pdf %}
                                    <i class="fas fa-file-pdf text-danger me-2"></i>
                                {% elif document.is_document %}
                                    <i class="fas fa-file-word text-primary me-2"></i>
                                {% elif document.is_spreadsheet %}
                                    <i class="fas fa-file-excel text-success me-2"></i>
                                {% else %}
                                    <i class="fas fa-file text-secondary me-2"></i>
                                {% endif %}
                                <div>
                                    <strong>{{ document.title|truncatechars:30 }}</strong>
                                    {% if document.is_confidential and user.user_type in 'adjuster,admin' %}
                                        <i class="fas fa-lock text-warning ms-1" title="Confidential"></i>
                                    {% endif %}
                                    {% if document.is_approved %}
                                        <i class="fas fa-check-circle text-success ms-1" title="Approved"></i>
                                    {% endif %}
                                    <br>
                                    <small class="text-muted">
                                        {{ document.get_document_type_display }} • {{ document.file_size_formatted }}
                                    </small>
                                </div>
                            </div>
                            <div class="btn-group" role="group">
                                <a href="{% url 'documents:view' document.id %}"
                                   class="btn btn-sm btn-outline-primary"
                                   title="View">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'documents:download' document.id %}"
                                   class="btn btn-sm btn-outline-success"
                                   title="Download">
                                    <i class="fas fa-download"></i>
                                </a>
                            </div>
                        </div>
                    {% endfor %}

                    {% if documents.count > 5 %}
                        <div class="text-center mt-3">
                            <a href="{% url 'documents:list' claim.id %}" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-ellipsis-h me-2"></i>View All {{ documents.count }} Documents
                            </a>
                        </div>
                    {% endif %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-folder-open fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-2">No documents uploaded yet</p>
                        <a href="{% url 'documents:upload' claim.id %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-upload me-2"></i>Upload First Document
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
