from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse, Http404, FileResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods, require_POST
from django.core.exceptions import PermissionDenied
from django.conf import settings
from django.utils.decorators import method_decorator
from django.views import View
import json
import os
import mimetypes

from claims.models import Claim
from .models import Document
from .forms import DocumentUploadForm, DocumentEditForm


@login_required
def upload_document(request, claim_id):
    """Upload a document for a claim"""
    claim = get_object_or_404(Claim, id=claim_id)

    # Check permissions
    if request.user.user_type == 'customer' and claim.claimant != request.user:
        messages.error(request, 'You can only upload documents to your own claims.')
        return redirect('claims:detail', claim_id=claim_id)

    if request.method == 'POST':
        form = DocumentUploadForm(request.POST, request.FILES, user=request.user)
        if form.is_valid():
            document = form.save(commit=False)
            document.claim = claim
            document.uploaded_by = request.user
            document.save()

            messages.success(request, f'Document "{document.title}" uploaded successfully!')
            return redirect('claims:detail', claim_id=claim_id)
    else:
        form = DocumentUploadForm(user=request.user)

    context = {
        'form': form,
        'claim': claim,
    }
    return render(request, 'documents/upload.html', context)


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def ajax_upload_document(request, claim_id):
    """AJAX endpoint for Dropzone file uploads"""
    claim = get_object_or_404(Claim, id=claim_id)

    # Check permissions
    if request.user.user_type == 'customer' and claim.claimant != request.user:
        return JsonResponse({'error': 'Permission denied'}, status=403)

    if 'file' not in request.FILES:
        return JsonResponse({'error': 'No file provided'}, status=400)

    file = request.FILES['file']
    document_type = request.POST.get('document_type', 'other')
    is_confidential = request.POST.get('is_confidential') == 'true'

    # Only staff can mark as confidential
    if request.user.user_type not in ['adjuster', 'admin']:
        is_confidential = False

    try:
        # Create document with file name as title initially
        title = os.path.splitext(file.name)[0]

        document = Document.objects.create(
            claim=claim,
            uploaded_by=request.user,
            title=title,
            document_type=document_type,
            file=file,
            is_confidential=is_confidential,
            original_filename=file.name
        )

        return JsonResponse({
            'success': True,
            'document_id': document.id,
            'title': document.title,
            'file_size': document.file_size_formatted,
            'document_type': document.get_document_type_display(),
            'created_at': document.created_at.strftime('%Y-%m-%d %H:%M')
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)


@login_required
def download_document(request, document_id):
    """Download a document"""
    document = get_object_or_404(Document, id=document_id)

    # Check permissions
    if not document.can_be_viewed_by(request.user):
        raise PermissionDenied("You don't have permission to view this document.")

    try:
        # Get the file path
        file_path = document.file.path

        if not os.path.exists(file_path):
            messages.error(request, 'File not found.')
            return redirect('claims:detail', claim_id=document.claim.id)

        # Determine content type
        content_type, _ = mimetypes.guess_type(file_path)
        if content_type is None:
            content_type = 'application/octet-stream'

        # Create response
        response = FileResponse(
            open(file_path, 'rb'),
            content_type=content_type,
            as_attachment=True,
            filename=document.original_filename or os.path.basename(file_path)
        )

        return response

    except Exception as e:
        messages.error(request, f'Error downloading file: {str(e)}')
        return redirect('claims:detail', claim_id=document.claim.id)


@login_required
@require_POST
def delete_document(request, document_id):
    """Delete a document"""
    document = get_object_or_404(Document, id=document_id)
    claim_id = document.claim.id

    # Check permissions
    if not document.can_be_deleted_by(request.user):
        messages.error(request, "You don't have permission to delete this document.")
        return redirect('claims:detail', claim_id=claim_id)

    try:
        # Delete the file from storage
        if document.file and os.path.exists(document.file.path):
            os.remove(document.file.path)

        document_title = document.title
        document.delete()

        messages.success(request, f'Document "{document_title}" deleted successfully.')

    except Exception as e:
        messages.error(request, f'Error deleting document: {str(e)}')

    return redirect('claims:detail', claim_id=claim_id)


@login_required
def edit_document(request, document_id):
    """Edit document metadata"""
    document = get_object_or_404(Document, id=document_id)

    # Check permissions
    if not document.can_be_viewed_by(request.user):
        messages.error(request, "You don't have permission to edit this document.")
        return redirect('claims:detail', claim_id=document.claim.id)

    if request.method == 'POST':
        form = DocumentEditForm(request.POST, instance=document, user=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, f'Document "{document.title}" updated successfully!')
            return redirect('claims:detail', claim_id=document.claim.id)
    else:
        form = DocumentEditForm(instance=document, user=request.user)

    context = {
        'form': form,
        'document': document,
        'claim': document.claim,
    }
    return render(request, 'documents/edit.html', context)


@login_required
def document_list(request, claim_id):
    """List all documents for a claim"""
    claim = get_object_or_404(Claim, id=claim_id)

    # Check permissions
    if request.user.user_type == 'customer' and claim.claimant != request.user:
        messages.error(request, 'You can only view documents for your own claims.')
        return redirect('claims:detail', claim_id=claim_id)

    documents = claim.documents.all()

    # Filter confidential documents for customers
    if request.user.user_type == 'customer':
        documents = documents.filter(is_confidential=False)

    context = {
        'claim': claim,
        'documents': documents,
    }
    return render(request, 'documents/list.html', context)


@login_required
def view_document(request, document_id):
    """View document details and preview if possible"""
    document = get_object_or_404(Document, id=document_id)

    # Check permissions
    if not document.can_be_viewed_by(request.user):
        raise PermissionDenied("You don't have permission to view this document.")

    context = {
        'document': document,
        'claim': document.claim,
    }
    return render(request, 'documents/view.html', context)