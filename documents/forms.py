from django import forms
from django.core.exceptions import ValidationError
from .models import Document


class DocumentUploadForm(forms.ModelForm):
    """Form for uploading documents"""

    class Meta:
        model = Document
        fields = ['title', 'description', 'document_type', 'file', 'is_confidential']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter document title'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Optional description'
            }),
            'document_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx,.txt,.rtf,.jpg,.jpeg,.png,.gif,.bmp,.tiff,.xls,.xlsx,.csv,.zip,.rar'
            }),
            'is_confidential': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Only staff can mark documents as confidential
        if self.user and self.user.user_type not in ['adjuster', 'admin']:
            self.fields.pop('is_confidential')

    def clean_file(self):
        file = self.cleaned_data.get('file')
        if file:
            # Additional file validation
            if file.size > 10 * 1024 * 1024:  # 10MB
                raise ValidationError("File size cannot exceed 10MB")

            # Check file extension
            allowed_extensions = Document.ALLOWED_EXTENSIONS
            file_extension = file.name.split('.')[-1].lower()
            if file_extension not in allowed_extensions:
                raise ValidationError(
                    f"File type not allowed. Allowed types: {', '.join(allowed_extensions)}"
                )

        return file

    def clean_title(self):
        title = self.cleaned_data.get('title')
        if title:
            title = title.strip()
            if len(title) < 3:
                raise ValidationError("Title must be at least 3 characters long")
        return title


class DocumentEditForm(forms.ModelForm):
    """Form for editing document metadata"""

    class Meta:
        model = Document
        fields = ['title', 'description', 'document_type', 'is_confidential', 'is_approved']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3
            }),
            'document_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'is_confidential': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'is_approved': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Only staff can edit confidential and approval status
        if self.user and self.user.user_type not in ['adjuster', 'admin']:
            self.fields.pop('is_confidential', None)
            self.fields.pop('is_approved', None)


class BulkDocumentUploadForm(forms.Form):
    """Form for handling multiple file uploads via Dropzone"""

    document_type = forms.ChoiceField(
        choices=Document.DOCUMENT_TYPES,
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        initial='other'
    )
    is_confidential = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Only staff can mark documents as confidential
        if self.user and self.user.user_type not in ['adjuster', 'admin']:
            self.fields.pop('is_confidential')
