from django.urls import path
from . import views

app_name = 'documents'

urlpatterns = [
    # Document management for claims
    path('claim/<int:claim_id>/', views.document_list, name='list'),
    path('claim/<int:claim_id>/upload/', views.upload_document, name='upload'),
    path('claim/<int:claim_id>/ajax-upload/', views.ajax_upload_document, name='ajax_upload'),

    # Individual document operations
    path('<int:document_id>/', views.view_document, name='view'),
    path('<int:document_id>/edit/', views.edit_document, name='edit'),
    path('<int:document_id>/download/', views.download_document, name='download'),
    path('<int:document_id>/delete/', views.delete_document, name='delete'),
]
