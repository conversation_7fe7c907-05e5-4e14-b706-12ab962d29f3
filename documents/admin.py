from django.contrib import admin
from .models import Document, DocumentVersion


@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    list_display = ('title', 'claim', 'document_type', 'uploaded_by', 'file_size_formatted', 'is_approved', 'is_confidential', 'created_at')
    list_filter = ('document_type', 'is_approved', 'is_confidential', 'created_at', 'uploaded_by')
    search_fields = ('title', 'description', 'claim__claim_number', 'uploaded_by__username')
    readonly_fields = ('file_size', 'original_filename', 'created_at', 'updated_at')

    fieldsets = (
        ('Document Information', {
            'fields': ('title', 'description', 'document_type', 'file')
        }),
        ('Claim Association', {
            'fields': ('claim', 'uploaded_by')
        }),
        ('File Information', {
            'fields': ('original_filename', 'file_size'),
            'classes': ('collapse',)
        }),
        ('Status & Permissions', {
            'fields': ('is_required', 'is_approved', 'is_confidential')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def file_size_formatted(self, obj):
        return obj.file_size_formatted
    file_size_formatted.short_description = 'File Size'


@admin.register(DocumentVersion)
class DocumentVersionAdmin(admin.ModelAdmin):
    list_display = ('document', 'version_number', 'uploaded_by', 'created_at')
    list_filter = ('created_at', 'uploaded_by')
    search_fields = ('document__title', 'change_notes', 'uploaded_by__username')
    readonly_fields = ('created_at',)
