from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import FileExtensionValidator
import os
import mimetypes


def validate_file_size(value):
    """Validate file size - max 10MB"""
    filesize = value.size
    if filesize > 10 * 1024 * 1024:  # 10MB
        raise ValidationError("File size cannot exceed 10MB")
    return value


def document_upload_path(instance, filename):
    """Generate upload path for documents"""
    # Organize by claim number and year
    claim_number = instance.claim.claim_number
    year = instance.claim.created_at.year
    return f'claim_documents/{year}/{claim_number}/{filename}'


class Document(models.Model):
    """Document model for claim-related files"""

    DOCUMENT_TYPES = (
        ('photo', 'Photo/Image'),
        ('police_report', 'Police Report'),
        ('medical_report', 'Medical Report'),
        ('estimate', 'Repair Estimate'),
        ('receipt', 'Receipt'),
        ('form', 'Insurance Form'),
        ('invoice', 'Invoice'),
        ('contract', 'Contract'),
        ('correspondence', 'Correspondence'),
        ('other', 'Other'),
    )

    # Allowed file extensions
    ALLOWED_EXTENSIONS = [
        'pdf', 'doc', 'docx', 'txt', 'rtf',  # Documents
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff',  # Images
        'xls', 'xlsx', 'csv',  # Spreadsheets
        'zip', 'rar'  # Archives
    ]

    claim = models.ForeignKey('claims.Claim', on_delete=models.CASCADE, related_name='documents')
    uploaded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)

    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPES, default='other')
    file = models.FileField(
        upload_to=document_upload_path,
        validators=[
            validate_file_size,
            FileExtensionValidator(allowed_extensions=ALLOWED_EXTENSIONS)
        ]
    )
    file_size = models.PositiveIntegerField(null=True, blank=True)
    original_filename = models.CharField(max_length=255, blank=True)

    is_required = models.BooleanField(default=False)
    is_approved = models.BooleanField(default=False)
    is_confidential = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        if self.file:
            self.file_size = self.file.size
            if not self.original_filename:
                self.original_filename = self.file.name
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.title} - {self.claim.claim_number}"

    @property
    def file_extension(self):
        return os.path.splitext(self.file.name)[1].lower()

    @property
    def is_image(self):
        return self.file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']

    @property
    def is_pdf(self):
        return self.file_extension == '.pdf'

    @property
    def is_document(self):
        return self.file_extension in ['.pdf', '.doc', '.docx', '.txt', '.rtf']

    @property
    def is_spreadsheet(self):
        return self.file_extension in ['.xls', '.xlsx', '.csv']

    @property
    def file_size_formatted(self):
        """Return human-readable file size"""
        if not self.file_size:
            return "Unknown"

        for unit in ['B', 'KB', 'MB', 'GB']:
            if self.file_size < 1024.0:
                return f"{self.file_size:.1f} {unit}"
            self.file_size /= 1024.0
        return f"{self.file_size:.1f} TB"

    @property
    def mime_type(self):
        """Get MIME type of the file"""
        if self.file:
            mime_type, _ = mimetypes.guess_type(self.file.name)
            return mime_type
        return None

    def can_be_viewed_by(self, user):
        """Check if user can view this document"""
        # Claim owner can view all non-confidential documents
        if self.claim.claimant == user and not self.is_confidential:
            return True

        # Staff can view all documents
        if user.user_type in ['adjuster', 'admin']:
            return True

        # Document uploader can view their own documents
        if self.uploaded_by == user:
            return True

        return False

    def can_be_deleted_by(self, user):
        """Check if user can delete this document"""
        # Only staff or document uploader can delete
        if user.user_type in ['adjuster', 'admin']:
            return True

        if self.uploaded_by == user and self.claim.status in ['submitted', 'under_review']:
            return True

        return False


class DocumentVersion(models.Model):
    """Version control for documents"""
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='versions')
    version_number = models.PositiveIntegerField(default=1)
    file = models.FileField(upload_to='document_versions/')
    uploaded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    change_notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-version_number']
        unique_together = ['document', 'version_number']

    def __str__(self):
        return f"{self.document.title} v{self.version_number}"
