# Generated by Django 4.2.21 on 2025-05-29 18:40

import django.core.validators
from django.db import migrations, models
import documents.models


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0002_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='document',
            name='is_confidential',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='document',
            name='original_filename',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='document',
            name='document_type',
            field=models.CharField(choices=[('photo', 'Photo/Image'), ('police_report', 'Police Report'), ('medical_report', 'Medical Report'), ('estimate', 'Repair Estimate'), ('receipt', 'Receipt'), ('form', 'Insurance Form'), ('invoice', 'Invoice'), ('contract', 'Contract'), ('correspondence', 'Correspondence'), ('other', 'Other')], default='other', max_length=20),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='document',
            name='file',
            field=models.FileField(upload_to=documents.models.document_upload_path, validators=[documents.models.validate_file_size, django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'doc', 'docx', 'txt', 'rtf', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'xls', 'xlsx', 'csv', 'zip', 'rar'])]),
        ),
    ]
